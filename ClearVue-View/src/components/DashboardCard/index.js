/* eslint-disable react/prop-types */
import React from 'react';
import { Box } from '@chakra-ui/react';
import { colors } from '../../theme';

const DashboardCard = function ({ children, fullWidth, height, minW, isDark = true, ...props }) {
  return (
    <Box
      h={height || '260px'}
      bg={isDark ? colors.dark.cardBackground : colors.main.white}
      color={isDark ? colors.dark.textPrimary : colors.main.black}
      border={isDark ? `1px solid ${colors.dark.border}` : 'none'}
      width="100%"
      {...props}
      minW={minW || '180px'}>
      {children}
    </Box>
  );
};

export default DashboardCard;
