import styled from 'styled-components';
import { colors } from '../../theme';

// eslint-disable-next-line import/prefer-default-export
export const StyledCard = styled.div`
  position: relative;
  padding: 10px;
  border-radius: 10px;
  background-color: ${(props) => (props.isDark ? colors.dark.cardBackground : colors.main.white)};
  color: ${(props) => (props.isDark ? colors.dark.textPrimary : colors.main.black)};
  border: ${(props) => (props.isDark ? `1px solid ${colors.dark.border}` : 'none')};
  // overflow: hidden;
`;
