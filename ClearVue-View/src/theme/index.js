import { extendTheme } from '@chakra-ui/react';

export const colors = {
  main: {
    primary: '#124684',
    semiPrimary: '#426b9d',
    lightPrimary: '#426b9d1f',
    secondary: '#DCDCDC',
    black: '#000000',
    white: '#ffffff',
    gray: '#A0AEC0',
    error: '#F56565',
    blue: '#322fed',
    grayBackground: '#f6f6f6',
    blueBackground: '#E9F4FF',
    positiveGreen: '#64C047',
    negativeRed: '#F17A68',
    mobileBackground: '#0072fe'
  },
  dark: {
    primary: '#4A90E2',
    semiPrimary: '#6BA3E8',
    lightPrimary: '#6BA3E81f',
    secondary: '#2D3748',
    black: '#ffffff',
    white: '#1A202C',
    gray: '#718096',
    error: '#F56565',
    blue: '#4299E1',
    grayBackground: '#2D3748',
    blueBackground: '#2A4365',
    positiveGreen: '#68D391',
    negativeRed: '#FC8181',
    mobileBackground: '#2B6CB0',
    cardBackground: '#2D3748',
    textPrimary: '#E2E8F0',
    textSecondary: '#A0AEC0',
    border: '#4A5568'
  }
};
// do not remove this
export const breakpoints = {
  sm: '40em', // 640px
  md: '52em', // 832px
  lg: '64em', // 1024px
  xl: '98em', // 1440px
  '2xl': '113em' // 1680px
};

const theme = extendTheme({ colors, breakpoints });

export default theme;
